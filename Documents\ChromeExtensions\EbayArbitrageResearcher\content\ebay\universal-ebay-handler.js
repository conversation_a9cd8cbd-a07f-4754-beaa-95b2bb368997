/**
 * ✅ UNIVERSAL EBAY HANDLER - Handles basic messages on all eBay pages
 * Loads at document_start for immediate availability
 * Based on EbayLister4 reference pattern
 */

(function() {
  'use strict';
  
  console.log('🌐 Universal eBay Handler loading at document_start...');
  
  /**
   * Handle basic messages that don't require complex dependencies
   */
  function handleMessage(message, sender, sendResponse) {
    console.log('📨 Universal eBay Handler received message:', message.action);
    
    try {
      // Handle ping requests
      if (message.action === 'ping') {
        console.log('🏓 Universal handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'universal-ebay-handler',
          url: window.location.href,
          timestamp: Date.now()
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Universal handler confirming ready');
        sendResponse({
          success: true,
          ready: true,
          handler: 'universal-ebay-handler',
          url: window.location.href
        });
        return true;
      }
      
      // Handle startScraping directly as universal fallback
      if (message.action === 'startScraping') {
        console.log('🔄 Universal handler: Handling startScraping directly as fallback');
        console.log('🔍 Universal handler: Message details:', message);
        console.log('🔍 Universal handler: Current URL:', window.location.href);
        console.log('🔍 Universal handler: Page title:', document.title);
        console.log('🔍 Universal handler: Document ready state:', document.readyState);

        // ✅ CRITICAL FIX: Proper async handling with sendResponse in scope
        handleScrapingWithTimeout(message, sendResponse);
        return true; // Keep message channel open
      }

      // ✅ FIXED: Always handle startScraping in universal handler
      // The search handler delegation was causing the scraping to fail
      console.log('🔄 Universal handler: All messages handled by universal handler for reliability');
      


      // Handle testSelectors for debugging
      if (message.action === 'testSelectors') {
        console.log('🧪 Testing eBay selectors...');
        const testResults = testEbaySelectors();
        sendResponse({
          success: true,
          results: testResults,
          handler: 'universal-ebay-handler'
        });
        return true;
      }

      // Handle other basic messages
      console.log('❓ Universal handler - unhandled message:', message.action);
      sendResponse({
        success: false,
        error: 'Message not handled by universal handler',
        action: message.action,
        handler: 'universal-ebay-handler',
        suggestion: 'This message might need the search page handler'
      });
      
    } catch (error) {
      console.error('❌ Universal handler error:', error);
      sendResponse({
        success: false,
        error: 'Universal handler crashed: ' + error.message,
        action: message.action
      });
    }
    
    return true;
  }
  
  /**
   * ✅ CRITICAL FIX: Proper async scraping handler with timeout protection
   */
  async function handleScrapingWithTimeout(message, sendResponse) {
    console.log('🚀 Starting scraping with timeout protection...');

    // Set up timeout protection
    const timeoutId = setTimeout(() => {
      console.error('⏰ Scraping timeout after 12 seconds');
      sendResponse({
        success: false,
        error: 'Scraping timeout after 12 seconds',
        products: [],
        handler: 'universal-ebay-handler-timeout'
      });
    }, 12000); // 12 second timeout (less than service worker's 15s)

    try {
      // Quick selector test first - UPDATED TO CURRENT EBAY STRUCTURE
      console.log('🔍 Quick selector test with current eBay selectors...');
      const testSelector = 'li.s-card.s-card--horizontal';
      const testElements = document.querySelectorAll(testSelector);
      console.log(`🔍 Found ${testElements.length} elements with "${testSelector}"`);

      if (testElements.length === 0) {
        clearTimeout(timeoutId);
        console.log('❌ No products found - page may not be loaded or selectors changed');
        sendResponse({
          success: true,
          products: [],
          error: 'No products found on page',
          handler: 'universal-ebay-handler',
          debug: {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState,
            totalElements: document.querySelectorAll('*').length
          }
        });
        return;
      }

      // Extract products using streamlined logic
      console.log(`🚀 Extracting products from ${testElements.length} elements...`);
      const products = await worldClassExtraction(Array.from(testElements));

      clearTimeout(timeoutId);
      console.log(`✅ Extraction completed: ${products.length} products`);

      sendResponse({
        success: true,
        products: products,
        handler: 'universal-ebay-handler',
        action: message.action,
        totalFound: products.length,
        url: window.location.href,
        timestamp: Date.now()
      });

    } catch (error) {
      clearTimeout(timeoutId);
      console.error('❌ Scraping failed:', error);
      sendResponse({
        success: false,
        error: 'Scraping failed: ' + error.message,
        products: [],
        handler: 'universal-ebay-handler',
        stack: error.stack
      });
    }
  }

  /**
   * 🚀 WORLD-CLASS EXTRACTION - Single unified extraction logic with comprehensive data
   */
  async function worldClassExtraction(listings) {
    console.log(`🚀 World-class extraction processing ${listings.length} listings...`);

    const products = [];
    const maxToProcess = Math.min(listings.length, 50); // Limit to prevent hanging

    for (let i = 0; i < maxToProcess; i++) {
      try {
        const listing = listings[i];

        // Quick validation
        if (!listing || !listing.querySelector) {
          console.warn(`⚠️ Invalid listing element at index ${i}`);
          continue;
        }

        // 🚀 CURRENT EBAY SELECTORS - Based on actual li.s-card structure

        // Extract core data first - FIXED: title element IS the link element
        const titleElement = listing.querySelector('.s-card__title');
        const title = titleElement ? titleElement.innerText.trim() : null;
        const url = titleElement ? titleElement.href : null;
        const price = extractCardPrice(listing);
        const shipping = extractCardShipping(listing);
        const condition = extractCardCondition(listing);
        const imageUrl = extractCardImage(listing);
        const soldDate = extractText(listing, '.s-card__ended-date');
        const seller = extractCardSeller(listing);
        const sellerFeedback = extractCardSellerFeedback(listing);
        const listingId = extractListingId(listing);
        const badges = extractCardBadges(listing);

        // Build data object with extracted values
        const data = {
          index: i,
          extractedAt: new Date().toISOString(),

          // Core product data
          title: title,
          url: url,
          price: price,
          shipping: shipping,
          condition: condition,
          imageUrl: imageUrl,
          soldDate: soldDate,

          // Current eBay seller info
          seller: seller,
          sellerFeedback: sellerFeedback,
          listingId: listingId,
          badges: badges,

          // Legacy fields for compatibility
          location: extractText(listing, '.s-card__location'),
          bids: null, // Not typically shown in s-card format
          bestOfferAccepted: price && typeof price === 'string' && price.includes('Best Offer'),
          freeReturns: badges.includes('Free returns'),
          category: extractText(listing, '.s-card__subtitle'),
          charity: badges.some(badge => badge.toLowerCase().includes('charity'))
        };

        // Skip promotional items
        if (data.title && data.title.includes('Shop on eBay')) {
          console.log(`⏭️ Skipping promotional item ${i}: "${data.title}"`);
          continue;
        }

        // Validate minimum required data
        if (!data.title || !data.url) {
          console.log(`⚠️ Skipping item ${i}: Missing required data (title: ${!!data.title}, url: ${!!data.url})`);
          continue;
        }

        // Calculate data quality
        data.extractionQuality = calculateQuality(data);
        data.source = 'universal-handler-world-class';

        console.log(`📦 PRODUCT ${i + 1}: "${data.title}" - $${data.price} - ${data.condition} (${data.extractionQuality}% complete)`);
        products.push(data);

      } catch (error) {
        console.warn(`⚠️ Failed to extract product ${i}:`, error);
      }

      // Progress logging every 10 items
      if ((i + 1) % 10 === 0) {
        console.log(`📊 Progress: ${i + 1}/${maxToProcess} products processed, ${products.length} valid products found`);
      }
    }

    console.log(`✅ World-class extraction completed: ${products.length}/${listings.length} products extracted`);
    return products;
  }

  // Helper extraction functions
  function extractText(container, selector) {
    try {
      const element = container.querySelector(selector);
      return element ? element.textContent.trim() : null;
    } catch (error) {
      return null;
    }
  }

  function extractAttribute(container, selector, attribute) {
    try {
      const element = container.querySelector(selector);
      return element ? element.getAttribute(attribute) : null;
    } catch (error) {
      return null;
    }
  }

  // 🚀 CURRENT EBAY CARD EXTRACTION FUNCTIONS

  function extractCardPrice(container) {
    const priceElement = container.querySelector('.s-card__price');
    if (!priceElement) return null;

    // Price can be complex (e.g., "$47.49Was: $49.99"). We only want the primary price.
    let price = priceElement.innerText.split(' ')[0].trim();

    // Handle cases where there was a best offer instead of a fixed price
    const bestOfferElement = container.querySelector('.s-card__price--accepted');
    if (bestOfferElement) {
      price = bestOfferElement.innerText.trim();
    }

    // Extract numeric value
    const match = price.match(/[\d,]+\.?\d*/);
    return match ? parseFloat(match[0].replace(/,/g, '')) : null;
  }

  function extractCardShipping(container) {
    const shippingElement = container.querySelector('.s-card__shipping, .s-card__delivery-cost');
    if (!shippingElement) return null;

    if (shippingElement.innerText.toLowerCase().includes('free')) {
      return 'Free';
    } else {
      // Extracts the price, e.g., "+$13.42 delivery" -> "$13.42"
      const match = shippingElement.innerText.match(/\$(\d+\.\d+)/);
      return match ? match[0] : null;
    }
  }

  function extractCardCondition(container) {
    const conditionElement = container.querySelector('.s-card__subtitle');
    if (!conditionElement) return null;

    // The condition is often the first part of the subtitle text.
    return conditionElement.innerText.split('·')[0].trim();
  }

  function extractCardImage(container) {
    const imageElement = container.querySelector('img.s-card__image-img');
    if (!imageElement) return null;

    let imageUrl = imageElement.src;

    // eBay often uses low-res thumbnails in search. Replace for high-res version.
    if (imageUrl.includes('s-l225')) {
      imageUrl = imageUrl.replace('s-l225', 's-l1600');
    } else if (imageUrl.includes('s-l300')) {
      imageUrl = imageUrl.replace('s-l300', 's-l1600');
    }

    return imageUrl;
  }

  function extractCardSeller(container) {
    const sellerElement = container.querySelector('.s-card__seller-info-text');
    if (!sellerElement) return null;

    return sellerElement.innerText.split(' ')[0].trim();
  }

  function extractCardSellerFeedback(container) {
    const sellerElement = container.querySelector('.s-card__seller-info-text');
    if (!sellerElement) return null;

    const match = sellerElement.innerText.match(/(\d+\.\d+%|\d+%)/);
    return match ? match[0] : null;
  }

  function extractListingId(container) {
    return container.dataset.listingid || null;
  }

  function extractCardBadges(container) {
    const badges = [];
    const badgeElements = container.querySelectorAll('.s-card__badge-icon');
    badgeElements.forEach(badge => {
      const badgeText = badge.innerText.trim();
      if (badgeText) {
        badges.push(badgeText);
      }
    });
    return badges;
  }

  // Legacy extraction functions for compatibility
  function extractPrice(container) {
    return extractCardPrice(container);
  }

  function extractShipping(container) {
    return extractCardShipping(container);
  }

  function extractImage(container) {
    return extractCardImage(container);
  }

  function extractBids(container) {
    const bidsText = extractText(container, 'span.s-item__bids');
    if (!bidsText) return null;

    const bidMatch = bidsText.match(/(\d+)\s*(bid|bids)/i);
    if (bidMatch) return { count: parseInt(bidMatch[1]), type: 'bids' };

    const watcherMatch = bidsText.match(/(\d+)\s*(watcher|watchers)/i);
    if (watcherMatch) return { count: parseInt(watcherMatch[1]), type: 'watchers' };

    return null;
  }

  function extractBoolean(container, selector) {
    try {
      return container.querySelector(selector) !== null;
    } catch (error) {
      return false;
    }
  }

  function extractFreeReturns(container) {
    const returnsElement = container.querySelector('.s-item__free-returns, .s-item__returns-accepted');
    if (!returnsElement) return false;

    const text = returnsElement.textContent?.toLowerCase() || '';
    return text.includes('free') || text.includes('return');
  }

  function calculateQuality(data) {
    const fields = ['title', 'price', 'url', 'condition', 'seller', 'location', 'imageUrl', 'shipping'];
    const filledFields = fields.filter(field => data[field] !== null && data[field] !== '');
    return Math.round((filledFields.length / fields.length) * 100);
  }

  /**
   * Test eBay selectors for debugging
   */
  function testEbaySelectors() {
    console.log('🧪 Testing CURRENT eBay selectors...');

    const selectors = {
      containers: 'li.s-card.s-card--horizontal',
      title: '.s-card__title', // Note: This element IS also the link
      price: '.s-card__price',
      condition: '.s-card__subtitle',
      seller: '.s-card__seller-info-text',
      image: 'img.s-card__image-img',
      shipping: '.s-card__shipping, .s-card__delivery-cost',
      badges: '.s-card__badge-icon'
    };

    const results = {};

    Object.entries(selectors).forEach(([name, selector]) => {
      const elements = document.querySelectorAll(selector);
      results[name] = {
        selector: selector,
        found: elements.length,
        sample: elements.length > 0 ? elements[0].textContent?.trim().substring(0, 100) : null
      };
      console.log(`🔍 ${name}: ${elements.length} elements found`);
    });

    return results;
  }

  /**
   * Check if current page is a search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }
  
  /**
   * Check if message is search-specific
   */
  function isSearchSpecificMessage(action) {
    const searchActions = [
      'startScraping',
      'testSelectors', 
      'minimalTest',
      'scanCurrentPage'
    ];
    return searchActions.includes(action);
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Send ready notification to service worker
  function notifyServiceWorker() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'universal-ebay-handler',
      url: window.location.href,
      timestamp: Date.now(),
      isSearchPage: isSearchPage()
    }).catch(error => {
      console.log('📡 Service worker not ready yet for universal handler');
    });
  }
  
  // Notify service worker when ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', notifyServiceWorker);
  } else {
    notifyServiceWorker();
  }
  
  // Global status for debugging
  window.universalEbayHandler = {
    isReady: () => true,
    getUrl: () => window.location.href,
    isSearchPage: isSearchPage
  };
  
  console.log('✅ Universal eBay Handler setup complete');
  
})();
