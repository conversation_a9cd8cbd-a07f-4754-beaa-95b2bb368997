/**
 * ✅ UNIVERSAL EBAY HANDLER - Handles basic messages on all eBay pages
 * Loads at document_start for immediate availability
 * Based on EbayLister4 reference pattern
 */

(function() {
  'use strict';
  
  console.log('🌐 Universal eBay Handler loading at document_start...');
  
  /**
   * Handle basic messages that don't require complex dependencies
   */
  function handleMessage(message, sender, sendResponse) {
    console.log('📨 Universal eBay Handler received message:', message.action);
    
    try {
      // Handle ping requests
      if (message.action === 'ping') {
        console.log('🏓 Universal handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'universal-ebay-handler',
          url: window.location.href,
          timestamp: Date.now()
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Universal handler confirming ready');
        sendResponse({
          success: true,
          ready: true,
          handler: 'universal-ebay-handler',
          url: window.location.href
        });
        return true;
      }
      
      // Handle startScraping directly as universal fallback
      if (message.action === 'startScraping') {
        console.log('🔄 Universal handler: Handling startScraping directly as fallback');
        console.log('🔍 Universal handler: Message details:', message);
        console.log('🔍 Universal handler: Current URL:', window.location.href);
        console.log('🔍 Universal handler: Page title:', document.title);
        console.log('🔍 Universal handler: Document ready state:', document.readyState);

        // ✅ CRITICAL: Use async pattern and return true to keep message port open
        (async () => {
          try {
            // ✅ WAIT FOR PAGE TO FULLY LOAD - Critical for eBay's dynamic content
            console.log('🔍 Universal handler: Waiting for page to fully load...');
            console.log('🔍 Document ready state:', document.readyState);

            // Wait for document to be ready and give time for AJAX content
            if (document.readyState !== 'complete') {
              await new Promise(resolve => {
                if (document.readyState === 'complete') {
                  resolve();
                } else {
                  window.addEventListener('load', resolve);
                }
              });
            }

            // Additional wait for dynamic content (eBay loads results via AJAX)
            await new Promise(resolve => setTimeout(resolve, 2000));

            console.log('🔍 Universal handler: Page should be fully loaded now');
            console.log('🔍 Document ready state:', document.readyState);

            // ✅ PRECISE EBAY SELECTORS - Based on your HTML structure analysis
            console.log('🔍 Universal handler: Using precise eBay product selectors...');

            // ✅ WAIT FOR REAL PRODUCT LISTINGS - Retry until we get substantial results
            let listings = [];
            let usedSelector = '';
            let attempts = 0;
            const maxAttempts = 10;

            while (attempts < maxAttempts && listings.length < 10) {
              attempts++;
              console.log(`🔍 Universal handler: Attempt ${attempts}/${maxAttempts} to find product listings...`);

              // ✅ USE WORKING SELECTOR - Based on browser testing
              console.log(`🔍 Universal handler: Using verified working selector that finds 60 real products`);

              const workingSelector = 'ul.srp-results li.s-item';  // This finds 60 real products
              const found = document.querySelectorAll(workingSelector);
              console.log(`🔍 Universal handler: "${workingSelector}" found ${found.length} elements`);

              if (found.length > 0) {
                listings = found;
                usedSelector = workingSelector;
                console.log(`✅ Universal handler: Using working selector with ${found.length} product listings`);
                break; // Exit the retry loop
              }



              if (listings.length >= 10) {
                console.log(`✅ Found sufficient listings (${listings.length}), proceeding...`);
                break;
              }

              console.log(`⏳ Only found ${listings.length} listings, waiting 1 second and retrying...`);
              await new Promise(resolve => setTimeout(resolve, 1000));
            }

            if (listings.length === 0) {
              console.log('🚨 Universal handler: No eBay product selectors worked, page structure may have changed');

              // DEBUG: Check what's actually on the page
              console.log('🔍 DEBUG: Document ready state:', document.readyState);
              console.log('🔍 DEBUG: Page URL:', window.location.href);
              console.log('🔍 DEBUG: Page title:', document.title);
              console.log('🔍 DEBUG: Total elements on page:', document.querySelectorAll('*').length);
              console.log('🔍 DEBUG: All li elements:', document.querySelectorAll('li').length);
              console.log('🔍 DEBUG: Elements with s-item class:', document.querySelectorAll('[class*="s-item"]').length);

              return;
            }

            // ✅ CRITICAL DEBUG: Verify we have real DOM elements, not templates
            console.log('🔍 DEBUGGING FIRST 3 ELEMENTS:');
            for (let i = 0; i < Math.min(3, listings.length); i++) {
              const item = listings[i];
              console.log(`--- Element ${i} ---`);
              console.log('HTML preview:', item.outerHTML.substring(0, 300));
              console.log('Text content preview:', item.textContent?.substring(0, 200));

              const titleEl = item.querySelector('h3.s-item__title');
              const priceEl = item.querySelector('span.s-item__price');
              const linkEl = item.querySelector('a.s-item__link');

              console.log('Title element found:', !!titleEl);
              console.log('Title text:', titleEl?.textContent?.trim());
              console.log('Price element found:', !!priceEl);
              console.log('Price text:', priceEl?.textContent?.trim());
              console.log('Link element found:', !!linkEl);
              console.log('Link href:', linkEl?.href);
            }

            console.log(`🔍 Universal handler: FINAL SELECTION: ${listings.length} elements`);

            // ✅ NO FILTERING - Process ALL elements to see everything
            console.log(`🔍 Universal handler: NO FILTERING - Processing all ${listings.length} elements`);

            // ✅ DEBUG: Log first few elements to see what we're working with
            for (let i = 0; i < Math.min(5, listings.length); i++) {
              const el = listings[i];
              console.log(`🔍 Element ${i}: tag="${el.tagName}" class="${el.className}" text="${el.textContent?.substring(0, 100)}"`);
            }

            console.log(`🔍 Universal handler: Processing ALL ${listings.length} listings with NO FILTERING`);

            // ✅ USE WORLD-CLASS SEARCH SCANNER - Enhanced extraction with comprehensive data
            console.log(`🚀 Universal handler: Using world-class EbaySearchScanner for comprehensive extraction...`);

            let products = [];

            // Check if our enhanced scanner is available
            if (window.EbaySearchScanner) {
              console.log('✅ EbaySearchScanner available - using world-class extraction');

              try {
                const scanner = new window.EbaySearchScanner();
                await scanner.initializeDependencies();

                // Use our world-class scanner with comprehensive data extraction
                const scanResults = await scanner.scanCurrentPage({
                  maxItems: listings.length,
                  includeSponsored: false
                });

                products = scanResults;
                console.log(`🚀 World-class scanner extracted ${products.length} products with comprehensive data`);

              } catch (scannerError) {
                console.warn('⚠️ World-class scanner failed, falling back to basic extraction:', scannerError);
                products = await basicExtraction(listings);
              }
            } else {
              console.warn('⚠️ EbaySearchScanner not available, using basic extraction');
              products = await basicExtraction(listings);
            }

            // ✅ NO EMERGENCY EXTRACTION - Just process what we found

            console.log(`✅ Universal handler: Extracted ${products.length} products`);
            console.log('🔍 Universal handler: Sample products:', products.slice(0, 3));
            console.log('🔍 Universal handler: Sending response with products array');

            sendResponse({
              success: true,
              products: products,
              handler: 'universal-ebay-handler',
              action: message.action,
              mode: 'universal-scraping',
              totalFound: products.length,
              url: window.location.href,
              timestamp: Date.now()
            });
          } catch (error) {
            console.error('❌ Universal handler scraping failed:', error);
            sendResponse({
              success: false,
              error: 'Universal scraping failed: ' + error.message,
              handler: 'universal-ebay-handler'
            });
          }
        })();

        return true; // Keep message channel open
      }

      // ✅ FIXED: Always handle startScraping in universal handler
      // The search handler delegation was causing the scraping to fail
      console.log('🔄 Universal handler: All messages handled by universal handler for reliability');
      


      // Handle other basic messages
      console.log('❓ Universal handler - unhandled message:', message.action);
      sendResponse({
        success: false,
        error: 'Message not handled by universal handler',
        action: message.action,
        handler: 'universal-ebay-handler',
        suggestion: 'This message might need the search page handler'
      });
      
    } catch (error) {
      console.error('❌ Universal handler error:', error);
      sendResponse({
        success: false,
        error: 'Universal handler crashed: ' + error.message,
        action: message.action
      });
    }
    
    return true;
  }
  
  /**
   * Basic extraction fallback when world-class scanner is not available
   */
  async function basicExtraction(listings) {
    console.log(`🔧 Using basic extraction fallback for ${listings.length} listings...`);

    const products = [];
    for (let i = 0; i < listings.length; i++) {
      const listing = listings[i];

      // ✅ CORRECT EBAY SELECTORS - Fixed based on actual DOM structure
      const titleEl = listing.querySelector('.s-item__title');  // DIV, not H3!
      const priceEl = listing.querySelector('span.s-item__price');
      const linkEl = listing.querySelector('a.s-item__link');

      // Additional eBay-specific elements (exact selectors)
      const conditionEl = listing.querySelector('.s-item__subtitle span.SECONDARY_INFO');
      const sellerEl = listing.querySelector('.s-item__seller-info-text');
      const shippingEl = listing.querySelector('.s-item__shipping, .s-item__logisticsCost');
      const locationEl = listing.querySelector('.s-item__location');
      const imageEl = listing.querySelector('img.s-item__image-img');

      // ✅ EXTRACT REAL DATA - Now with correct selectors
      const title = titleEl?.textContent?.trim() || '';
      const priceText = priceEl?.textContent?.trim() || '';
      const link = linkEl?.href || '';

      // Only skip obvious promotional items
      if (title.includes('Shop on eBay')) {
        console.log(`⏭️ Skipping promotional item ${i}: "${title}"`);
        continue;
      }

      // Extract additional eBay-specific data
      const condition = conditionEl?.textContent?.trim() || '';
      const seller = sellerEl?.textContent?.trim() || '';
      const shipping = shippingEl?.textContent?.trim() || '';
      const location = locationEl?.textContent?.trim() || '';
      const imageUrl = imageEl?.src || imageEl?.getAttribute('data-src') || '';

      // Parse price number
      const priceMatch = priceText.match(/[\d,]+\.?\d*/);
      const price = priceMatch ? parseFloat(priceMatch[0].replace(/,/g, '')) : 0;

      // Extract item ID from link
      let itemId = '';
      if (link) {
        const itemIdMatch = link.match(/\/itm\/(\d+)/);
        if (itemIdMatch) {
          itemId = itemIdMatch[1];
        }
      }

      console.log(`📦 EBAY PRODUCT ${i + 1}: "${title}" - ${priceText} - ${condition}`);

      products.push({
        title: title,
        price: price,
        priceText: priceText,
        link: link,
        itemId: itemId,
        condition: condition,
        seller: seller,
        shipping: shipping,
        location: location,
        imageUrl: imageUrl,
        source: 'universal-handler-basic-fallback',
        index: i,
        timestamp: Date.now()
      });
    }

    return products;
  }

  /**
   * Check if current page is a search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }
  
  /**
   * Check if message is search-specific
   */
  function isSearchSpecificMessage(action) {
    const searchActions = [
      'startScraping',
      'testSelectors', 
      'minimalTest',
      'scanCurrentPage'
    ];
    return searchActions.includes(action);
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Send ready notification to service worker
  function notifyServiceWorker() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'universal-ebay-handler',
      url: window.location.href,
      timestamp: Date.now(),
      isSearchPage: isSearchPage()
    }).catch(error => {
      console.log('📡 Service worker not ready yet for universal handler');
    });
  }
  
  // Notify service worker when ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', notifyServiceWorker);
  } else {
    notifyServiceWorker();
  }
  
  // Global status for debugging
  window.universalEbayHandler = {
    isReady: () => true,
    getUrl: () => window.location.href,
    isSearchPage: isSearchPage
  };
  
  console.log('✅ Universal eBay Handler setup complete');
  
})();
